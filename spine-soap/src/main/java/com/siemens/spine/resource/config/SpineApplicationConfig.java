package com.siemens.spine.resource.config;

import com.siemens.spine.resource.exceptionhandler.ForbiddenErrorHandler;
import com.siemens.spine.resource.exceptionhandler.JsonMappingExceptionHandler;
import com.siemens.spine.resource.exceptionhandler.ServerErrorHandler;
import com.siemens.spine.resource.exceptionhandler.SpineExceptionHandler;
import com.siemens.spine.resource.exceptionhandler.UnauthorizedHandler;
import com.siemens.spine.resource.exceptionhandler.ValidationErrorHandler;
import com.siemens.spine.resource.rest.RestRepositoryResource;
import com.siemens.spine.resource.rest.ChangeGroupResource;
import com.siemens.spine.resource.rest.ComponentResource;
import com.siemens.spine.resource.rest.DynamicQueryResource;
import com.siemens.spine.resource.rest.GroupResource;
import com.siemens.spine.resource.rest.IamResource;
import com.siemens.spine.resource.rest.ProjectResource;
import com.siemens.spine.resource.rest.TypeResource;
import com.siemens.spine.resource.rest.VersionControlResource;
import org.glassfish.jersey.jackson.JacksonFeature;

import javax.enterprise.context.ApplicationScoped;
import javax.ws.rs.ApplicationPath;
import javax.ws.rs.core.Application;
import java.util.Set;

/**
 * <AUTHOR> Pham
 * @version 1.0
 * @since 22/12/2022
 */
@ApplicationScoped
@ApplicationPath("/api/v1")
public class SpineApplicationConfig extends Application {

    @Override
    public Set<Class<?>> getClasses() {

        return Set.of(
                // register jackson mapper
                CustomJacksonMapperProvider.class,

                // register resource
                ProjectResource.class,
                ComponentResource.class,
                ChangeGroupResource.class,
                GroupResource.class,
                TypeResource.class,
                IamResource.class,
                VersionControlResource.class,
                DynamicQueryResource.class,
                RestRepositoryResource.class,

                // exception handler
                JsonMappingExceptionHandler.class,
                SpineExceptionHandler.class,
                ValidationErrorHandler.class,
                UnauthorizedHandler.class,
                ForbiddenErrorHandler.class,
                ServerErrorHandler.class
        );
    }

    @Override
    public Set<Object> getSingletons() {
        return Set.of(
                JacksonFeature.withoutExceptionMappers()
        );
    }

}
