package com.siemens.spine.resource.rest;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.siemens.spine.db.repository.CrudRepository;
import com.siemens.spine.logic.service.RestEndpointGenerator;

import javax.inject.Inject;
import javax.persistence.Id;
import javax.ws.rs.Consumes;
import javax.ws.rs.DELETE;
import javax.ws.rs.GET;
import javax.ws.rs.POST;
import javax.ws.rs.PUT;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import javax.ws.rs.core.UriInfo;
import java.lang.reflect.Field;
import java.net.URI;
import java.util.List;

@Path("/data-rest")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
public class RestRepositoryResource {

    @Inject
    private RestEndpointGenerator endpointGenerator;

    @GET
    @Path("/{entityPath}")
    public Response getAll(@PathParam("entityPath") String entityPath) {
        RestEndpointGenerator.RestEndpointInfo endpoint = getRestEndpointInfo(entityPath);
        if (endpoint == null) {
            return Response.status(Response.Status.NOT_FOUND).build();
        }

        @SuppressWarnings("unchecked")
        CrudRepository<Object, Object> repository = (CrudRepository<Object, Object>) endpoint.repository();
        List<Object> entities = repository.findAll();

        return Response.ok(entities).build();
    }

    @GET
    @Path("/{entityPath}/{id}")
    public Response getById(@PathParam("entityPath") String entityPath, @PathParam("id") String idStr) {
        RestEndpointGenerator.RestEndpointInfo endpoint = getRestEndpointInfo(entityPath);
        if (endpoint == null) {
            return Response.status(Response.Status.NOT_FOUND).build();
        }

        Object id = convertId(idStr, endpoint.idClass());

        @SuppressWarnings("unchecked")
        CrudRepository<Object, Object> repository = (CrudRepository<Object, Object>) endpoint.repository();

        return repository.findById(id).map(entity -> Response.ok(entity).build())
                .orElse(Response.status(Response.Status.NOT_FOUND).build());
    }

    @POST
    @Path("/{entityPath}")
    public Response create(@PathParam("entityPath") String entityPath, String jsonBody, @Context UriInfo uriInfo) {
        RestEndpointGenerator.RestEndpointInfo endpoint = getRestEndpointInfo(entityPath);
        if (endpoint == null) {
            return Response.status(Response.Status.NOT_FOUND).build();
        }

        try {
            ObjectMapper mapper = new ObjectMapper();
            Object entity = mapper.readValue(jsonBody, endpoint.entityClass());

            @SuppressWarnings("unchecked")
            CrudRepository<Object, Object> repository = (CrudRepository<Object, Object>) endpoint.repository();
            Object savedEntity = repository.save(entity);

            // Get ID for location header
            Object id = extractId(savedEntity);
            URI location = uriInfo.getAbsolutePathBuilder().path(String.valueOf(id)).build();

            return Response.created(location).entity(savedEntity).build();
        } catch (Exception e) {
            return Response.status(Response.Status.BAD_REQUEST).entity("Invalid JSON: " + e.getMessage()).build();
        }
    }

    @PUT
    @Path("/{entityPath}/{id}")
    public Response update(@PathParam("entityPath") String entityPath, @PathParam("id") String idStr, String jsonBody) {
        RestEndpointGenerator.RestEndpointInfo endpoint = getRestEndpointInfo(entityPath);
        if (endpoint == null) {
            return Response.status(Response.Status.NOT_FOUND).build();
        }

        Object id = convertId(idStr, endpoint.idClass());

        @SuppressWarnings("unchecked")
        CrudRepository<Object, Object> repository = (CrudRepository<Object, Object>) endpoint.repository();

        if (!repository.existsById(id)) {
            return Response.status(Response.Status.NOT_FOUND).build();
        }

        try {
            ObjectMapper mapper = new ObjectMapper();
            Object entity = mapper.readValue(jsonBody, endpoint.entityClass());

            // Set the ID on the entity using reflection
            setIdOnEntity(entity, id);

            Object updatedEntity = repository.save(entity);
            return Response.ok(updatedEntity).build();
        } catch (Exception e) {
            return Response.status(Response.Status.BAD_REQUEST).entity("Invalid JSON: " + e.getMessage()).build();
        }
    }

    private RestEndpointGenerator.RestEndpointInfo getRestEndpointInfo(String entityPath) {
        RestEndpointGenerator.RestEndpointInfo endpoint = findEndpoint(entityPath);
        return endpoint;
    }

    @DELETE
    @Path("/{entityPath}/{id}")
    public Response delete(@PathParam("entityPath") String entityPath, @PathParam("id") String idStr) {
        RestEndpointGenerator.RestEndpointInfo endpoint = getRestEndpointInfo(entityPath);
        if (endpoint == null) {
            return Response.status(Response.Status.NOT_FOUND).build();
        }

        Object id = convertId(idStr, endpoint.idClass());

        @SuppressWarnings("unchecked")
        CrudRepository<Object, Object> repository = (CrudRepository<Object, Object>) endpoint.repository();

        if (!repository.existsById(id)) {
            return Response.status(Response.Status.NOT_FOUND).build();
        }

        repository.deleteById(id);
        return Response.noContent().build();
    }

    private RestEndpointGenerator.RestEndpointInfo findEndpoint(String entityPath) {
        return endpointGenerator.getGeneratedEndpoints().stream()
                .filter(info -> info.path().equals("/" + entityPath)).findFirst().orElse(null);
    }

    private Object convertId(String idStr, Class<?> idClass) {
        if (idClass == Long.class || idClass == long.class) {
            return Long.parseLong(idStr);
        } else if (idClass == Integer.class || idClass == int.class) {
            return Integer.parseInt(idStr);
        } else if (idClass == String.class) {
            return idStr;
        }
        // Add more type conversions as needed
        return idStr;
    }

    private Object extractId(Object entity) {
        // Use reflection to find @Id annotated field
        Class<?> clazz = entity.getClass();
        for (Field field : clazz.getDeclaredFields()) {
            if (field.isAnnotationPresent(Id.class)) {
                field.setAccessible(true);
                try {
                    return field.get(entity);
                } catch (IllegalAccessException e) {
                    // Handle exception
                }
            }
        }
        return null;
    }

    private void setIdOnEntity(Object entity, Object id) {
        // Use reflection to set @Id annotated field
        Class<?> clazz = entity.getClass();
        for (Field field : clazz.getDeclaredFields()) {
            if (field.isAnnotationPresent(Id.class)) {
                field.setAccessible(true);
                try {
                    field.set(entity, id);
                    break;
                } catch (IllegalAccessException e) {
                    // Handle exception
                }
            }
        }
    }

}