package com.siemens.spine.db.repository.elcon;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.TYPE)
public @interface RestRepository {

    String path() default "";

    String collectionResourceRel() default "";

    boolean exported() default true;

}
