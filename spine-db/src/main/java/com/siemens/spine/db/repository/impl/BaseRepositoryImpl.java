package com.siemens.spine.db.repository.impl;

import com.siemens.spine.db.exception.JpaException;
import com.siemens.spine.db.repository.CrudRepository;
import lombok.extern.slf4j.Slf4j;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.TypedQuery;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import java.util.ArrayList;
import java.util.List;

/**
 * Base repository implementation with common query methods
 *
 * @param <T>  The entity type
 * @param <ID> The ID type
 */
@Slf4j
public abstract class BaseRepositoryImpl<T, ID> implements CrudRepository<T, ID> {

    @PersistenceContext
    protected EntityManager entityManager;

    /**
     * Get the entity class
     *
     * @return The entity class
     */
    protected abstract Class<T> getEntityClass();

    /**
     * Find all entities with optional predicates
     *
     * @param predicates Optional predicates to filter the results
     * @return List of entities matching the predicates
     */
    protected List<T> findAll(Predicate... predicates) {
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<T> cq = cb.createQuery(getEntityClass());
        Root<T> root = cq.from(getEntityClass());

        if (predicates != null && predicates.length > 0) {
            cq.where(predicates);
        }

        CriteriaQuery<T> all = cq.select(root);
        TypedQuery<T> query = entityManager.createQuery(all);

        return query.getResultList();
    }

    /**
     * Find entities with pagination
     *
     * @param page       The page number (0-based)
     * @param size       The page size
     * @param predicates Optional predicates to filter the results
     * @return List of entities for the requested page
     */
    protected List<T> findWithPagination(int page, int size, Predicate... predicates) {
        if (page < 0 || size <= 0) {
            throw new JpaException("Invalid pagination parameters: page must be >= 0 and size must be > 0");
        }

        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<T> cq = cb.createQuery(getEntityClass());
        Root<T> root = cq.from(getEntityClass());

        if (predicates != null && predicates.length > 0) {
            cq.where(predicates);
        }

        CriteriaQuery<T> all = cq.select(root);
        TypedQuery<T> query = entityManager.createQuery(all);

        query.setFirstResult(page * size);
        query.setMaxResults(size);

        return query.getResultList();
    }

    /**
     * Count entities matching the given predicates
     *
     * @param predicates Optional predicates to filter the results
     * @return The count of matching entities
     */
    protected long count(Predicate... predicates) {
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<Long> cq = cb.createQuery(Long.class);
        Root<T> root = cq.from(getEntityClass());

        cq.select(cb.count(root));

        if (predicates != null && predicates.length > 0) {
            cq.where(predicates);
        }

        TypedQuery<Long> query = entityManager.createQuery(cq);
        Long result = query.getSingleResult();

        return result == null ? 0 : result;
    }

    /**
     * Create predicates for a field matching any of the given values
     *
     * @param cb        The criteria builder
     * @param root      The root entity
     * @param fieldName The field name
     * @param values    The values to match
     * @return A predicate for the field matching any of the values
     */
    protected Predicate createInPredicate(CriteriaBuilder cb, Root<T> root, String fieldName, List<?> values) {
        if (values == null || values.isEmpty()) {
            return null;
        }

        return root.get(fieldName).in(values);
    }

    /**
     * Create predicates for multiple fields
     *
     * @param cb          The criteria builder
     * @param root        The root entity
     * @param fieldValues Map of field names to values
     * @return List of predicates for the fields
     */
    protected List<Predicate> createPredicates(CriteriaBuilder cb,
                                               Root<T> root,
                                               java.util.Map<String, Object> fieldValues) {
        List<Predicate> predicates = new ArrayList<>();

        if (fieldValues == null || fieldValues.isEmpty()) {
            return predicates;
        }

        for (java.util.Map.Entry<String, Object> entry : fieldValues.entrySet()) {
            String fieldName = entry.getKey();
            Object value = entry.getValue();

            if (value == null) {
                predicates.add(cb.isNull(root.get(fieldName)));
            } else if (value instanceof List) {
                Predicate inPredicate = createInPredicate(cb, root, fieldName, (List<?>) value);
                if (inPredicate != null) {
                    predicates.add(inPredicate);
                }
            } else {
                predicates.add(cb.equal(root.get(fieldName), value));
            }
        }

        return predicates;
    }

}

