package com.siemens.spine.db.repository.elcon;

import com.siemens.spine.db.entity.elcon.ElconObjectEntity;

import javax.enterprise.context.ApplicationScoped;

/**
 * <AUTHOR> Admin
 * @version : 1.0
 * @since : 19/6/2025
 **/
@RestRepository(path = "elconObjects", collectionResourceRel = "elconObjects")
@ApplicationScoped
public class ElconObjectRepository extends AbstractRestCRUDRepository<ElconObjectEntity, Long> {

    @Override
    protected boolean isNew(ElconObjectEntity entity) {
        return entity.getObjectId() == null;
    }

}
