package com.siemens.spine.db.repository;

import com.siemens.spine.db.entity.QueryTemplateEntity;

import javax.validation.constraints.NotNull;
import java.sql.SQLException;
import java.util.Map;
import java.util.Optional;

public interface QueryTemplateRepository extends GenericJpaRepository<QueryTemplateEntity, Long> {

    Optional<QueryTemplateEntity> findByTemplateName(@NotNull String templateName);

    Map<String, Object> executeQuerySafe(final QueryTemplateEntity template,
                                         Map<String, Object> parameters,
                                         Integer page,
                                         Integer size) throws SQLException;

    int getTotalCount(QueryTemplateEntity template, Map<String, Object> parameters) throws SQLException;

}

