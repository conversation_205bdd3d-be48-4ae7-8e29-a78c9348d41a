package com.siemens.spine.db.repository.elcon;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Root;
import javax.transaction.Transactional;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.lang.reflect.TypeVariable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

public abstract class AbstractCrudRepository<T, ID> implements CrudRepository<T, ID> {

    @PersistenceContext
    protected EntityManager entityManager;

    private final Class<T> entityClass;

    @SuppressWarnings("unchecked")
    protected AbstractCrudRepository() {
        Class<?>[] types = resolveTypeArguments(getClass(), AbstractCrudRepository.class);
        this.entityClass = (Class<T>) types[0];
    }

    @Override
    @Transactional
    public T save(T entity) {
        if (isNew(entity)) {
            entityManager.persist(entity);
            return entity;
        } else {
            return entityManager.merge(entity);
        }
    }

    @Override
    public Optional<T> findById(ID id) {
        return Optional.ofNullable(entityManager.find(entityClass, id));
    }

    @Override
    public List<T> findAll() {
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<T> query = cb.createQuery(entityClass);
        Root<T> root = query.from(entityClass);
        return entityManager.createQuery(query.select(root)).getResultList();
    }

    @Override
    @Transactional
    public void deleteById(ID id) {
        findById(id).ifPresent(entity -> entityManager.remove(entity));
    }

    @Override
    public boolean existsById(ID id) {
        return findById(id).isPresent();
    }

    @Override
    public long count() {
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<Long> query = cb.createQuery(Long.class);
        Root<T> root = query.from(entityClass);
        return entityManager.createQuery(query.select(cb.count(root))).getSingleResult();
    }

    protected abstract boolean isNew(T entity);

    /**
     * Resolves generic type arguments for the target superclass
     */
    private static Class<?>[] resolveTypeArguments(Class<?> clazz, Class<?> targetSuperClass) {
        return resolveTypeArguments(clazz, targetSuperClass, new HashMap<>());
    }

    private static Class<?>[] resolveTypeArguments(Class<?> clazz, Class<?> targetSuperClass,
                                                   Map<TypeVariable<?>, Type> typeVariableMap) {

        if (clazz == null || clazz == Object.class) {
            throw new IllegalArgumentException("Cannot resolve type arguments for " + targetSuperClass);
        }

        Type genericSuperclass = clazz.getGenericSuperclass();
        if (genericSuperclass instanceof ParameterizedType) {
            ParameterizedType parameterizedType = (ParameterizedType) genericSuperclass;

            if (parameterizedType.getRawType() == targetSuperClass) {
                Type[] actualTypeArguments = parameterizedType.getActualTypeArguments();
                Class<?>[] resolvedTypes = new Class<?>[actualTypeArguments.length];

                for (int i = 0; i < actualTypeArguments.length; i++) {
                    resolvedTypes[i] = resolveType(actualTypeArguments[i], typeVariableMap);
                }
                return resolvedTypes;
            } else {
                Class<?> rawType = (Class<?>) parameterizedType.getRawType();
                TypeVariable<?>[] typeParameters = rawType.getTypeParameters();
                Type[] actualTypeArguments = parameterizedType.getActualTypeArguments();

                Map<TypeVariable<?>, Type> newTypeVariableMap = new HashMap<>(typeVariableMap);
                for (int i = 0; i < typeParameters.length && i < actualTypeArguments.length; i++) {
                    newTypeVariableMap.put(typeParameters[i], actualTypeArguments[i]);
                }

                return resolveTypeArguments(rawType, targetSuperClass, newTypeVariableMap);
            }
        }

        // Continue with superclass
        return resolveTypeArguments(clazz.getSuperclass(), targetSuperClass, typeVariableMap);
    }

    private static Class<?> resolveType(Type type, Map<TypeVariable<?>, Type> typeVariableMap) {
        if (type instanceof Class<?>) {
            return (Class<?>) type;
        } else if (type instanceof TypeVariable<?>) {
            Type resolvedType = typeVariableMap.get(type);
            if (resolvedType instanceof Class<?>) {
                return (Class<?>) resolvedType;
            } else if (resolvedType instanceof TypeVariable<?>) {
                return resolveType(resolvedType, typeVariableMap);
            }
        } else if (type instanceof ParameterizedType) {
            return (Class<?>) ((ParameterizedType) type).getRawType();
        }

        throw new IllegalArgumentException("Cannot resolve type: " + type);
    }

}