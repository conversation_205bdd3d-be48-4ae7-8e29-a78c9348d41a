package com.siemens.spine.db.repository;

import java.util.List;
import java.util.Optional;

/**
 * This is the base crud repository for spine/elcon
 *
 * @param <T>  Entity
 * @param <ID> Data type of the entity ID
 */
public interface CrudRepository<T, ID> {

    T save(T entity);

    Optional<T> findById(ID id);

    List<T> findAll();

    void deleteById(ID id);

    boolean existsById(ID id);

    long count();

}