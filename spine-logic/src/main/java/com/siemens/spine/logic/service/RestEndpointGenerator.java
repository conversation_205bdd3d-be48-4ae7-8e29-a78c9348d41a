package com.siemens.spine.logic.service;

import com.siemens.spine.db.repository.CrudRepository;
import com.siemens.spine.db.repository.elcon.RestCRUDRepository;
import com.siemens.spine.db.repository.elcon.RestRepository;

import javax.annotation.PostConstruct;
import javax.enterprise.context.ApplicationScoped;
import javax.enterprise.inject.Any;
import javax.enterprise.inject.Instance;
import javax.inject.Inject;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.List;

@ApplicationScoped
public class RestEndpointGenerator {

    private final Instance<CrudRepository> beans;

    @Inject
    public RestEndpointGenerator(@Any Instance<CrudRepository> beans) {
        this.beans = beans;
    }

    private final List<RestEndpointInfo> generatedEndpoints = new ArrayList<>();

    @PostConstruct
    public void init() {
        generateEndpoints();
    }

    private void generateEndpoints() {
        for (Object repo : beans) {
            Class<?> beanClass = getBeanClass(repo);
            if (beanClass.getPackageName().equals("com.siemens.spine.db.repository.elcon")) {
                RestRepository annotation = findRestRepositoryAnnotation(beanClass);
                if (annotation != null && annotation.exported()) {
                    generateRestEndpoint(repo, annotation, beanClass);
                }
            }
        }
    }

    private Class<?> getBeanClass(Object proxy) {
        Class<?> proxyClass = proxy.getClass();
        if (proxyClass.getName().contains("$Proxy$")) {
            return proxyClass.getSuperclass() != Object.class ?
                    proxyClass.getSuperclass() :
                    proxyClass.getInterfaces()[0];
        }
        return proxyClass;
    }

    private RestRepository findRestRepositoryAnnotation(Class<?> clazz) {
        return clazz.getAnnotation(RestRepository.class);
    }

    private void generateRestEndpoint(Object repository, RestRepository annotation, Class<?> repositoryClass) {
        Class<?> entityClass = getEntityType(repositoryClass);
        Class<?> idClass = getIdType(repositoryClass);

        if (entityClass != null) {
            String path = annotation.path().isEmpty() ?
                    entityClass.getSimpleName().toLowerCase() + "s" :
                    annotation.path();

            RestEndpointInfo info = new RestEndpointInfo(repository, entityClass, idClass, "/" + path);
            generatedEndpoints.add(info);
        }
    }

    public Class<?>[] getGenericTypes(Class<?> clazz) {
        Type genericSuperclass = clazz.getGenericSuperclass();

        if (genericSuperclass instanceof ParameterizedType parameterizedType) {
            Type[] actualTypeArguments = parameterizedType.getActualTypeArguments();

            Class<?>[] genericTypes = new Class<?>[actualTypeArguments.length];
            for (int i = 0; i < actualTypeArguments.length; i++) {
                if (actualTypeArguments[i] instanceof Class<?>) {
                    genericTypes[i] = (Class<?>) actualTypeArguments[i];
                }
            }
            return genericTypes;
        }

        return new Class<?>[0];
    }

    public Class<?> getEntityType(Class<?> repositoryClass) {
        Class<?>[] types = getGenericTypes(repositoryClass);
        return types.length > 0 ? types[0] : null;
    }

    public Class<?> getIdType(Class<?> repositoryClass) {
        Class<?>[] types = getGenericTypes(repositoryClass);
        return types.length > 1 ? types[1] : null;
    }

    public List<RestEndpointInfo> getGeneratedEndpoints() {
        generateEndpoints();
        return generatedEndpoints;
    }

    /**
     * @param repository The rest endpoint repository instance
     */
    public record RestEndpointInfo(Object repository, Class<?> entityClass, Class<?> idClass, String path) {

    }

}